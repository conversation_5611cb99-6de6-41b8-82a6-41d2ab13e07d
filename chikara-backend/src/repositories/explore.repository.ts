import { db } from "../lib/db.js";
import { Prisma } from "@prisma/client";
import { handleError, handleInternalError } from "../utils/log.js";
import type { ExploreNodeLocation, ExploreNodeStatus, ExploreNodeType, TravelMethod } from "@prisma/client";
import type { MapNodePosition, TravelStatus } from "../features/explore/explore.types.js";

/**
 * Fetch static explore nodes by location
 */
export const getStaticNodesByLocation = async (location: ExploreNodeLocation) => {
    const nodes = await db.explore_static_node.findMany({
        where: { location },
        include: {
            shop: true,
        },
        orderBy: { id: "asc" },
    });

    return nodes.map((node) => {
        // If nodeType is SHOP and shop data is available, use shop's title and description
        const title = node.nodeType === "SHOP" && node.shop ? node.shop.name : node.title;
        const description = node.nodeType === "SHOP" && node.shop ? node.shop.description : node.description;

        return {
            id: node.id,
            nodeType: node.nodeType,
            title,
            description,
            position: node.position as MapNodePosition,
            metadata: node.metadata as Record<string, unknown> | undefined,
            location: node.location,
            shopId: node.shopId,
            expiresAt: undefined, // Static nodes don't expire
        };
    });
};

/**
 * Fetch active player nodes by location for a specific user
 */
export const getActivePlayerNodesByLocation = async (userId: number, location: ExploreNodeLocation) => {
    const nodes = await db.explore_player_node.findMany({
        where: {
            userId,
            location,
            status: {
                in: ["available", "locked", "current"],
            },
            OR: [
                {
                    expiresAt: {
                        gt: new Date(),
                    },
                },
                {
                    expiresAt: null, // Include nodes that never expire (like story nodes)
                },
            ],
        },
        orderBy: { createdAt: "desc" },
    });

    return nodes.map((node) => ({
        id: node.id,
        nodeType: node.nodeType,
        title: node.title,
        description: node.description,
        position: node.position as MapNodePosition,
        metadata: node.metadata as Record<string, unknown> | undefined,
        status: node.status,
        location: node.location,
        expiresAt: node.expiresAt,
    }));
};

/**
 * Create a new player-specific explore node
 */
export const createPlayerNode = async (
    userId: number,
    nodeType: ExploreNodeType,
    title: string,
    description: string,
    position: MapNodePosition,
    location: ExploreNodeLocation,
    expirationHours: number | null,
    metadata?: Record<string, unknown>,
    status: ExploreNodeStatus = "available"
) => {
    const expiresAt = new Date();

    if (expirationHours) {
        if (expirationHours <= 0 || expirationHours > 168) {
            return handleInternalError(`expirationHours must be between 1 and 168, received: ${expirationHours}`);
        }
        expiresAt.setHours(expiresAt.getHours() + expirationHours);
    }

    const node = await db.explore_player_node.create({
        data: {
            userId,
            nodeType,
            title,
            description,
            position,
            location,
            metadata: metadata ?? Prisma.DbNull,
            expiresAt: expirationHours ? expiresAt : null,
            status,
        },
    });

    return {
        ...node,
        position: node.position as MapNodePosition,
        metadata: node.metadata as Record<string, unknown> | undefined,
    };
};

/**
 * Delete expired player nodes for a specific user
 */
export const cleanupExpiredPlayerNodes = async (userId: number) => {
    const result = await db.explore_player_node.deleteMany({
        where: {
            userId,
            expiresAt: {
                lte: new Date(),
            },
        },
    });

    return result.count;
};

/**
 * Get a specific static node by ID
 */
export const getStaticNodeById = async (nodeId: number) => {
    const node = await db.explore_static_node.findUnique({
        where: { id: nodeId },
        include: {
            shop: true,
        },
    });

    if (!node) return null;

    // If nodeType is SHOP and shop data is available, use shop's title and description
    const title = node.nodeType === "SHOP" && node.shop ? node.shop.name : node.title;
    const description = node.nodeType === "SHOP" && node.shop ? node.shop.description : node.description;

    return {
        ...node,
        title,
        description,
        position: node.position as MapNodePosition,
        metadata: node.metadata as Record<string, unknown> | undefined,
    };
};

/**
 * Get a specific player node by ID and user ID
 */
export const getPlayerNodeById = async (nodeId: number, userId: number) => {
    const node = await db.explore_player_node.findFirst({
        where: {
            id: nodeId,
            userId,
            OR: [
                {
                    expiresAt: {
                        gt: new Date(),
                    },
                },
                {
                    expiresAt: null, // Include nodes that never expire (like story nodes)
                },
            ],
        },
    });

    if (!node) return null;

    return {
        ...node,
        position: node.position as MapNodePosition,
        metadata: node.metadata as Record<string, unknown> | undefined,
    };
};

/**
 * Count all player nodes for a user in a specific location
 */
export const countPlayerNodesByLocation = async (userId: number, location: ExploreNodeLocation) => {
    return await db.explore_player_node.count({
        where: {
            userId,
            location,
            status: {
                in: ["available", "locked", "current", "completed"],
            },
            OR: [
                {
                    expiresAt: {
                        gt: new Date(),
                    },
                },
                {
                    expiresAt: null, // Include nodes that never expire (like story nodes)
                },
            ],
        },
    });
};

/**
 * Get all player nodes for a user in a specific location
 */
export const getPlayerNodesByUserAndLocation = async (userId: number, location: ExploreNodeLocation) => {
    const nodes = await db.explore_player_node.findMany({
        where: {
            userId,
            location,
            OR: [
                {
                    expiresAt: {
                        gt: new Date(),
                    },
                },
                {
                    expiresAt: null, // Include nodes that never expire (like story nodes)
                },
            ],
        },
        orderBy: { id: "asc" },
    });

    return nodes.map((node) => ({
        ...node,
        position: node.position as MapNodePosition,
        metadata: node.metadata as Record<string, unknown> | undefined,
    }));
};

/**
 * Delete a player node
 */
export const deletePlayerNode = async (nodeId: number, userId: number): Promise<void> => {
    await db.explore_player_node.delete({
        where: { id: nodeId, userId },
    });
};

/**
 * Initiate travel to a new location with specified method and time delay
 */
export const initiateTravel = async (
    userId: number,
    newLocation: ExploreNodeLocation,
    method: TravelMethod,
    cost: number,
    travelTimeMinutes: number
) => {
    // Use a transaction to ensure both operations succeed or fail together
    const result = await db.$transaction(async (tx) => {
        // First check if user has enough cash and is not already traveling
        const user = await tx.user.findUnique({
            where: { id: userId },
            select: {
                cash: true,
                currentMapLocation: true,
                travelStartTime: true,
                travelEndTime: true,
            },
        });

        if (!user) {
            return handleError("User not found", 400);
        }

        if (user.cash < cost) {
            return handleError("Insufficient cash", 400);
        }

        if (user.currentMapLocation === newLocation) {
            return handleError("Already at this location", 400);
        }

        // Check if user is already traveling
        if (user.travelStartTime && user.travelEndTime && new Date() < user.travelEndTime) {
            return handleError("Already traveling to another location", 400);
        }

        // Calculate travel times
        const now = new Date();
        const travelEndTime = new Date(now.getTime() + travelTimeMinutes * 60 * 1000);

        // Update user's cash, location, and travel state
        const updatedUser = await tx.user.update({
            where: { id: userId },
            data: {
                cash: user.cash - cost,
                currentMapLocation: newLocation,
                travelStartTime: now,
                travelEndTime: travelEndTime,
                travelMethod: method,
            },
            select: {
                cash: true,
                currentMapLocation: true,
                travelStartTime: true,
                travelEndTime: true,
                travelMethod: true,
            },
        });

        return updatedUser;
    });

    return result;
};

/**
 * Get user's current travel status without any side effects
 * This function only reads data and does not modify the database
 */
export const getUserTravelStatusOnly = async (userId: number): Promise<TravelStatus> => {
    const user = await db.user.findUnique({
        where: { id: userId },
        select: {
            currentMapLocation: true,
            travelStartTime: true,
            travelEndTime: true,
            travelMethod: true,
        },
    });

    if (!user) {
        return handleError("User not found", 400);
    }

    const now = new Date();
    const isTravel = !!(user.travelStartTime && user.travelEndTime && now < user.travelEndTime);

    return {
        isTravel,
        travelingTo: isTravel ? user.currentMapLocation || undefined : undefined,
        travelStartTime: user.travelStartTime || undefined,
        travelEndTime: user.travelEndTime || undefined,
        travelMethod: user.travelMethod || undefined,
        remainingTime: isTravel && user.travelEndTime ? user.travelEndTime.getTime() - now.getTime() : undefined,
    };
};

/**
 * Auto-complete travel if time has elapsed
 * Returns information about whether travel was completed and the destination
 */
export const autoCompleteTravelIfNeeded = async (
    userId: number
): Promise<{ autoCompleted: boolean; completedDestination?: ExploreNodeLocation }> => {
    // Use a transaction to atomically check and complete travel to prevent race conditions
    const result = await db.$transaction(async (tx) => {
        const user = await tx.user.findUnique({
            where: { id: userId },
            select: {
                currentMapLocation: true,
                travelStartTime: true,
                travelEndTime: true,
                travelMethod: true,
            },
        });

        if (!user) {
            return handleError("User not found", 400);
        }

        const now = new Date();

        // Check if user is traveling and if travel time has elapsed
        if (user.travelEndTime && now >= user.travelEndTime) {
            // Atomically complete the travel by clearing travel fields only if they haven't been cleared yet
            // This prevents race conditions where multiple concurrent calls try to complete the same travel
            const updateResult = await tx.user.updateMany({
                where: {
                    id: userId,
                    travelEndTime: {
                        not: null,
                        lte: now,
                    },
                },
                data: {
                    travelStartTime: null,
                    travelEndTime: null,
                    travelMethod: null,
                },
            });

            // If we successfully updated (count > 0), this call was the one that completed the travel
            const autoCompleted = updateResult.count > 0;

            return {
                autoCompleted,
                completedDestination: autoCompleted ? user.currentMapLocation || undefined : undefined,
            };
        }

        // No travel to complete
        return {
            autoCompleted: false,
            completedDestination: undefined,
        };
    });

    return result;
};

/**
 * Get user's current travel status with optional auto-completion
 * @param userId - The user ID
 * @param autoComplete - Whether to auto-complete travel if time has elapsed (default: true for backward compatibility)
 */
export const getUserTravelStatus = async (
    userId: number,
    autoComplete = true
): Promise<TravelStatus & { autoCompleted?: boolean; completedDestination?: ExploreNodeLocation }> => {
    if (!autoComplete) {
        // Just get the status without any modifications
        const status = await getUserTravelStatusOnly(userId);
        return {
            ...status,
            autoCompleted: false,
        };
    }

    // First, try to auto-complete travel if needed
    const completionResult = await autoCompleteTravelIfNeeded(userId);

    // Then get the current status
    const status = await getUserTravelStatusOnly(userId);

    return {
        ...status,
        autoCompleted: completionResult.autoCompleted,
        completedDestination: completionResult.completedDestination,
    };
};

/**
 * Atomically lock a player node for interaction to prevent race conditions
 * Marks the node as "current" to prevent other concurrent interactions
 */
export const lockPlayerNodeForInteraction = async (nodeId: number, userId: number) => {
    return await db.$transaction(async (tx) => {
        // First, try to get the node and verify it's available
        const node = await tx.explore_player_node.findFirst({
            where: {
                id: nodeId,
                userId,
                status: {
                    in: ["available"],
                },
                OR: [
                    {
                        expiresAt: {
                            gt: new Date(),
                        },
                    },
                    {
                        expiresAt: null, // Include nodes that never expire (like story nodes)
                    },
                ],
            },
        });

        if (!node) {
            return null;
        }

        // Mark the node as "current" to lock it for this interaction
        const updatedNode = await tx.explore_player_node.updateMany({
            where: {
                id: nodeId,
                userId,
                status: { in: ["available"] },
            },
            data: {
                status: "current",
            },
        });

        // If no rows were updated, another transaction beat us to it
        if (updatedNode.count === 0) {
            return null;
        }

        // Return the node data for interaction processing
        return {
            ...node,
            position: node.position as MapNodePosition,
            metadata: node.metadata as Record<string, unknown> | undefined,
        };
    });
};

/**
 * Complete a locked player node interaction successfully
 */
export const completePlayerNodeInteraction = async (nodeId: number, userId: number, expirationMinutes: number) => {
    const expiresAt = new Date();
    expiresAt.setMinutes(expiresAt.getMinutes() + expirationMinutes);

    const result = await db.explore_player_node.updateMany({
        where: {
            id: nodeId,
            userId,
            status: "current",
        },
        data: {
            status: "completed",
            expiresAt,
        },
    });

    return result.count > 0;
};

/**
 * Unlock a player node if the interaction failed
 */
export const unlockPlayerNodeOnFailure = async (nodeId: number, userId: number) => {
    const result = await db.explore_player_node.updateMany({
        where: {
            id: nodeId,
            userId,
            status: "current",
        },
        data: {
            status: "available",
        },
    });

    return result.count > 0;
};

/**
 * Update player node metadata
 */
export const updatePlayerNodeMetadata = async (nodeId: number, userId: number, metadata: Record<string, unknown>) => {
    const result = await db.explore_player_node.updateMany({
        where: {
            id: nodeId,
            userId,
        },
        data: {
            metadata,
        },
    });

    return result.count > 0;
};

export const updatePlayerNodeStatus = async (nodeId: number, userId: number, status: ExploreNodeStatus) => {
    const result = await db.explore_player_node.updateMany({
        where: {
            id: nodeId,
            userId,
        },
        data: {
            status,
        },
    });

    return result.count > 0;
};
