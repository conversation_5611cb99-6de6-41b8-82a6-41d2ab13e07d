import useChangeMapLocation from "@/features/explore/api/useChangeMapLocation";

import useTravelStatus from "@/features/explore/api/useTravelStatus";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import { cn } from "@/lib/utils";
import { AlertCircle, Coins, Compass, Loader2, Map, MapPin, Train } from "lucide-react";
import { useState } from "react";
import { toast } from "react-hot-toast";
import { TOKYO_WARDS } from "../constants/wards";
import type { TravelMethod, ExploreNodeLocation } from "../types/explore.types";
import { MapBackground } from "./MapBackground";
import { TravelMethodModal } from "./TravelMethodModal";
import { usePersistStore } from "@/app/store/stores";

interface ExploreLocationsViewProps {
    className?: string;
}

export const ExploreLocationsView = ({ className }: ExploreLocationsViewProps) => {
    const { data: currentUser } = useFetchCurrentUser();
    const { mutate: changeLocation, isPending: isChangingLocation } = useChangeMapLocation();
    const { setExplorePageSetting } = usePersistStore();

    const { data: travelStatus, refetch: refetchTravelStatus } = useTravelStatus({
        refetchInterval: 1000, // Poll every second during travel
    });

    const [selectedWard, setSelectedWard] = useState<string | null>(null);
    const [showTravelMethodModal, setShowTravelMethodModal] = useState(false);
    const [selectedDestination, setSelectedDestination] = useState<string | null>(null);

    const currentLocation = currentUser?.currentMapLocation;
    const isTravel = travelStatus?.isTravel || false;

    const handleTravelTo = (wardId: string) => {
        const ward = TOKYO_WARDS.find((w) => w.id === wardId);
        if (!ward || !currentUser) return;

        if (wardId === currentLocation) {
            toast.error("You are already in this location!");
            return;
        }

        if (isTravel) {
            toast.error("You are already traveling!");
            return;
        }

        // Show travel method selection modal
        setSelectedDestination(wardId);
        setShowTravelMethodModal(true);
    };

    const handleMethodSelect = (method: TravelMethod) => {
        const ward = TOKYO_WARDS.find((w) => w.id === selectedDestination);
        if (!ward || !currentUser || !selectedDestination) return;

        const cost = method === "walk" ? 0 : ward.cost;

        if (currentUser.cash < cost) {
            toast.error(`You need ¥${cost} to travel by ${method}!`);
            return;
        }

        changeLocation(
            { location: selectedDestination as ExploreNodeLocation, method },
            {
                onSuccess: () => {
                    toast.success(`Started traveling to ${ward.name} by ${method}!`);
                    refetchTravelStatus();
                    // Immediately switch to map view to show the travel screen
                    setExplorePageSetting("map");
                },
                onError: (error: any) => {
                    toast.error(error?.message || "Failed to start travel");
                },
            }
        );
    };

    const getCurrentLocationName = () => {
        const ward = TOKYO_WARDS.find((w) => w.id === currentLocation);
        return ward?.name || "Unknown Location";
    };

    if (!currentUser) {
        return (
            <div className="flex items-center justify-center h-96 bg-gradient-to-br from-indigo-50 via-white to-cyan-50 dark:from-gray-950 dark:via-gray-900 dark:to-indigo-950/50 rounded-lg">
                <div className="flex items-center space-x-2">
                    <Loader2 className="w-8 h-8 animate-spin text-blue-500" />
                    <span className="text-gray-600 dark:text-gray-300">Loading user data...</span>
                </div>
            </div>
        );
    }

    return (
        <div className={cn("space-y-6", className)}>
            <div className="grid lg:grid-cols-3 gap-6">
                {/* Tokyo Map Visualization */}
                <div className="lg:col-span-2 bg-gray-900/70 rounded-lg border border-purple-900/30 overflow-hidden">
                    <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-xl rounded-lg shadow-2xl shadow-gray-900/10 dark:shadow-gray-900/30 border border-gray-200/50 dark:border-gray-700/50 overflow-hidden">
                        <div className="p-4 border-b border-gray-200/50 dark:border-gray-700/50 hidden lg:block">
                            <div className="flex items-center space-x-3 absolute z-10 bg-black/10 top-3 left-2 px-2 rounded-lg">
                                <Map className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                                <h3 className="text-lg font-bold text-gray-900 dark:text-white">Tokyo District Map</h3>
                            </div>
                            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                Click on any district to view details and travel options
                            </p>
                        </div>

                        {/* Interactive Map */}
                        <div className="relative h-80 lg:h-96">
                            {/* Map Background */}
                            <MapBackground currentView="tokyo" />

                            {/* Tokyo Ward Markers */}
                            {TOKYO_WARDS.map((ward) => (
                                <button
                                    key={ward.id}
                                    disabled={isTravel}
                                    className={cn(
                                        "absolute w-16 h-16 transform -translate-x-1/2 -translate-y-1/2 z-10 transition-all duration-300",
                                        selectedWard === ward.id ? "scale-110" : "hover:scale-105",
                                        isTravel && "opacity-50 cursor-not-allowed"
                                    )}
                                    style={{
                                        left: `${ward.position.x}%`,
                                        top: `${ward.position.y}%`,
                                    }}
                                    onClick={() => !isTravel && setSelectedWard(ward.id)}
                                >
                                    {/* Ward glow effect */}
                                    <div
                                        className={`absolute inset-0 rounded-lg blur-md opacity-60 bg-gradient-to-br ${ward.atmosphere.primaryColor}`}
                                        style={{
                                            transform: selectedWard === ward.id ? "scale(1.2)" : "scale(1)",
                                        }}
                                    />

                                    {/* Ward main button */}
                                    <div
                                        className={cn(
                                            "relative w-16 h-16 rounded-lg flex flex-col items-center justify-center p-1",
                                            ward.background,
                                            `border-2 ${ward.atmosphere.accentColor}`,
                                            selectedWard === ward.id && `${ward.atmosphere.glowColor} shadow-lg`
                                        )}
                                    >
                                        <MapPin className="w-6 h-6 text-white mb-1" />
                                        <span className="text-white text-xs font-medium text-center leading-tight">
                                            {ward.name}
                                        </span>
                                    </div>

                                    {/* Ward name with atmospheric styling */}
                                    <div
                                        className={cn(
                                            "absolute -bottom-8 left-1/2 transform -translate-x-1/2 whitespace-nowrap px-2 py-1 rounded-md text-xs font-medium",
                                            selectedWard === ward.id
                                                ? `bg-gradient-to-r ${ward.atmosphere.primaryColor} text-white shadow-lg`
                                                : "bg-gray-900/80 text-gray-300"
                                        )}
                                    >
                                        {ward.name}
                                        {selectedWard === ward.id && (
                                            <div className="absolute inset-0 rounded-md bg-white/20 animate-pulse" />
                                        )}
                                    </div>
                                </button>
                            ))}
                        </div>
                    </div>
                </div>

                {/* Ward Details Panel */}
                <div className="space-y-4">
                    {selectedWard ? (
                        (() => {
                            const ward = TOKYO_WARDS.find((w) => w.id === selectedWard);
                            if (!ward) return null;

                            const isCurrentLocation = ward.id === currentLocation;
                            const canAfford = currentUser.cash >= ward.cost;

                            return (
                                <div
                                    className={cn(
                                        "rounded-lg p-3 border-2",
                                        ward.atmosphere.accentColor,
                                        ward.background
                                    )}
                                >
                                    <div className="flex items-center justify-between mb-3">
                                        <div className="flex items-center gap-3">
                                            <div
                                                className={cn(
                                                    "w-12 h-12 rounded-lg flex items-center justify-center relative overflow-hidden",
                                                    ward.background,
                                                    `border-2 ${ward.atmosphere.accentColor}`
                                                )}
                                            >
                                                {/* Animated background for the icon */}
                                                <div
                                                    className={`absolute inset-0 bg-gradient-to-br ${ward.atmosphere.primaryColor} opacity-60 animate-pulse`}
                                                />
                                                <MapPin className="w-6 h-6 text-white relative z-10" />
                                            </div>
                                            <div>
                                                <h3 className="text-white font-semibold text-lg">{ward.name}</h3>
                                            </div>
                                        </div>
                                    </div>

                                    <p className="text-gray-300 text-sm mb-3 leading-relaxed">{ward.description}</p>

                                    {/* Features */}
                                    <div className="mb-3">
                                        <h4 className="text-white font-medium text-sm mb-2">Key Features</h4>
                                        <div className="flex flex-wrap gap-1">
                                            {ward.features.map((feature, index) => (
                                                <span
                                                    key={index}
                                                    className="px-2 py-1 bg-gray-800/60 text-gray-300 text-xs rounded-full border border-gray-600"
                                                >
                                                    {feature}
                                                </span>
                                            ))}
                                        </div>
                                    </div>

                                    {/* Travel Cost */}
                                    <div className="flex items-center space-x-1 bg-yellow-50 dark:bg-yellow-900/20 mb-4 px-3 py-2 rounded-lg border border-yellow-200 dark:border-yellow-800">
                                        <p className="text-sm font-semibold text-gray-900 dark:text-white mr-4">
                                            Cost:
                                        </p>
                                        <Coins className="w-4 h-4 text-yellow-600 dark:text-yellow-400" />
                                        <span className="font-semibold text-yellow-700 dark:text-yellow-300 text-sm">
                                            ¥{ward.cost}
                                        </span>
                                    </div>

                                    {/* Action Button */}
                                    {isCurrentLocation ? (
                                        <div className="flex items-center justify-center p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg border border-purple-200 dark:border-purple-800">
                                            <div className="flex items-center space-x-2 text-purple-700 dark:text-purple-300">
                                                <MapPin className="w-4 h-4" />
                                                <span className="font-semibold text-sm">You are here!</span>
                                            </div>
                                        </div>
                                    ) : (
                                        <button
                                            disabled={isTravel || isChangingLocation}
                                            className={cn(
                                                "w-full flex items-center justify-center space-x-2 px-4 py-3 rounded-lg font-semibold transition-all duration-200 text-sm",
                                                !isTravel && !isChangingLocation
                                                    ? "bg-gradient-to-r from-purple-600 to-indigo-700 hover:from-purple-700 hover:to-indigo-800 text-white shadow-lg shadow-purple-500/25 hover:shadow-xl hover:shadow-purple-500/30 transform hover:scale-105 active:scale-95"
                                                    : "bg-gray-200 dark:bg-gray-700 text-gray-500 dark:text-gray-400 cursor-not-allowed",
                                                "disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100"
                                            )}
                                            onClick={() => handleTravelTo(ward.id)}
                                        >
                                            {isChangingLocation ? (
                                                <>
                                                    <Loader2 className="w-4 h-4 animate-spin" />
                                                    <span>Starting Travel...</span>
                                                </>
                                            ) : isTravel ? (
                                                <>
                                                    <Train className="w-4 h-4" />
                                                    <span>Currently Traveling</span>
                                                </>
                                            ) : (
                                                <>
                                                    <Train className="w-4 h-4" />
                                                    <span>Choose Travel Method</span>
                                                </>
                                            )}
                                        </button>
                                    )}

                                    {!canAfford && !isCurrentLocation && (
                                        <div className="mt-2 flex items-center space-x-2 text-red-600 dark:text-red-400 text-xs">
                                            <AlertCircle className="w-3 h-3" />
                                            <span>
                                                You need ¥{(ward.cost - currentUser.cash).toLocaleString()} more
                                            </span>
                                        </div>
                                    )}
                                </div>
                            );
                        })()
                    ) : (
                        <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-xl rounded-lg lg:rounded-2xl shadow-2xl shadow-gray-900/10 dark:shadow-gray-900/30 border border-gray-200/50 dark:border-gray-700/50 overflow-hidden p-3 lg:p-6">
                            <div className="lg:text-center flex lg:flex-col items-center justify-center gap-x-2">
                                <Compass className="lg:w-12 w-8 h-auto text-gray-400 lg:mx-auto lg:mb-3" />
                                <h3 className="lg:text-lg font-semibold text-gray-900 dark:text-white lg:mb-2">
                                    Select a Tokyo District
                                </h3>
                                <p className="text-gray-600 dark:text-gray-400 text-sm hidden lg:block">
                                    Click on any district marker on the map to view details and travel options.
                                </p>
                            </div>
                        </div>
                    )}
                </div>
            </div>

            {/* Travel Method Selection Modal */}
            {selectedDestination && (
                <TravelMethodModal
                    isOpen={showTravelMethodModal}
                    destination={TOKYO_WARDS.find((w) => w.id === selectedDestination)!}
                    userCash={currentUser?.cash || 0}
                    isLoading={isChangingLocation}
                    onSelectMethod={handleMethodSelect}
                    onClose={() => {
                        setShowTravelMethodModal(false);
                        setSelectedDestination(null);
                    }}
                />
            )}
        </div>
    );
};
